package net.lab1024.sa.admin.module.business.nfc.ws.domain;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 卡片信息消息的载体。
 * 用于传输NFC卡片的基本信息，如卡片ID、类型等。
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // 序列化时忽略null字段
public class CardInfoPayload {

    /**
     * 卡片唯一标识符 (e.g., UID, 卡号)
     * 支持两种字段名格式：cardId (驼峰) 和 card_id (下划线)
     */
    @JsonProperty("cardId")
    @JsonAlias({"card_id"})
    private String cardId;

    /**
     * 卡片类型 (e.g., "MIFARE_CLASSIC", "MIFARE_ULTRALIGHT", "ISO14443_TYPE_A")
     * 支持两种字段名格式：cardType (驼峰) 和 card_type (下划线)
     */
    @JsonProperty("cardType")
    @JsonAlias({"card_type"})
    private String cardType;

    /**
     * 卡片技术类型 (e.g., "NfcA", "NfcB", "NfcF")
     */
    private String technology;

    /**
     * 卡片大小/容量信息 (可选)
     */
    private String size;

    /**
     * 卡片制造商信息 (可选)
     */
    private String manufacturer;

    /**
     * 附加的卡片数据或属性 (可选)
     */
    private Object additionalData;

    /**
     * 便利构造器：只设置基本的卡片ID和类型
     * @param cardId 卡片ID
     * @param cardType 卡片类型
     */
    public CardInfoPayload(String cardId, String cardType) {
        this.cardId = cardId;
        this.cardType = cardType;
    }

    /**
     * 便利构造器：设置卡片ID、类型和技术类型
     * @param cardId 卡片ID
     * @param cardType 卡片类型
     * @param technology 技术类型
     */
    public CardInfoPayload(String cardId, String cardType, String technology) {
        this.cardId = cardId;
        this.cardType = cardType;
        this.technology = technology;
    }
}
