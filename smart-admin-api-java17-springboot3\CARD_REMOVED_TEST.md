# CARD_REMOVED 功能测试说明

## 问题解决

### 原始错误
```
Cannot deserialize value of type `net.lab1024.sa.admin.module.business.nfc.ws.domain.MessageTypeEnum` from String "CARD_REMOVED": not one of the values accepted for Enum class
```

### 解决方案
1. **添加了 CARD_REMOVED 枚举值**到 `MessageTypeEnum.java`
2. **创建了 CardRemovedPayload 类**来处理卡片移除数据
3. **实现了 handleCardRemoved 方法**来处理卡片移除消息
4. **增强了日志记录**，包含详细的卡片移除信息

## 新增功能

### 1. MessageTypeEnum 扩展
```java
/**
 * 卡片移除消息
 */
CARD_REMOVED(15, "CARD_REMOVED"),
```

### 2. CardRemovedPayload 数据结构
```java
public class CardRemovedPayload {
    private String cardId;        // 移除的卡片ID
    private Long timestamp;       // 移除时间戳
    private String reason;        // 移除原因
    private String additionalInfo; // 额外信息
}
```

### 3. 消息处理流程
1. **接收消息**: 客户端发送 CARD_REMOVED 消息
2. **解析数据**: 解析 CardRemovedPayload 数据
3. **记录日志**: 详细记录卡片移除信息
4. **转发通知**: 将移除通知转发给伙伴设备
5. **确认回复**: 向发送方确认通知已发送

### 4. 日志输出示例
```
🚫 [处理卡片移除] employeeId=123, session=abc-def-123
🚫 [卡片移除详情] employeeId=123, cardId=869FD29B, reason=USER_REMOVED, timestamp=1691234567890
✅ [卡片移除通知成功] 从 employeeId=123 通知到 employeeId=456
```

## 测试方法

### 1. WebSocket 消息格式
```json
{
  "type": "CARD_REMOVED",
  "payload": {
    "card_id": "869FD29B",
    "timestamp": 1691234567890,
    "reason": "USER_REMOVED",
    "additional_info": "Card was physically removed by user"
  }
}
```

### 2. 预期行为
- ✅ 消息能够正确解析，不再出现反序列化错误
- ✅ 详细的日志记录卡片移除过程
- ✅ 伙伴设备收到移除通知
- ✅ 发送方收到确认消息

### 3. 错误处理
- 如果伙伴设备离线，记录警告日志但不抛出异常
- 如果payload解析失败，记录警告但继续处理
- 所有错误都有详细的上下文信息

## 兼容性

### 向后兼容
- 现有的消息类型不受影响
- 现有的客户端可以继续正常工作
- 新的CARD_REMOVED消息是可选的

### 客户端支持
客户端需要更新以支持：
1. 发送 CARD_REMOVED 消息
2. 接收和处理来自伙伴的 CARD_REMOVED 通知
3. 处理 CARD_REMOVED_SENT 状态更新

## 监控和调试

### 日志过滤
```bash
# 查看所有卡片移除相关日志
grep "卡片移除" logs/websocket.log

# 查看特定用户的卡片移除日志
grep "卡片移除.*employeeId=123" logs/websocket.log

# 统计卡片移除次数
grep -c "卡片移除通知成功" logs/websocket.log
```

### 分析脚本
使用提供的 `analyze-websocket-logs.sh` 脚本可以自动统计卡片移除相关的指标。

这个功能的添加解决了客户端发送 CARD_REMOVED 消息时的反序列化错误，并提供了完整的卡片移除通知机制。
