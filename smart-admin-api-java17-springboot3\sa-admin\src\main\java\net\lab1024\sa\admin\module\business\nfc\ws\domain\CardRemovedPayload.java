package net.lab1024.sa.admin.module.business.nfc.ws.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 卡片移除消息载荷
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CardRemovedPayload {
    
    /**
     * 移除的卡片ID（如果有的话）
     */
    private String cardId;
    
    /**
     * 移除时间戳
     */
    private Long timestamp;
    
    /**
     * 移除原因
     */
    private String reason;
    
    /**
     * 额外信息
     */
    private String additionalInfo;
}
