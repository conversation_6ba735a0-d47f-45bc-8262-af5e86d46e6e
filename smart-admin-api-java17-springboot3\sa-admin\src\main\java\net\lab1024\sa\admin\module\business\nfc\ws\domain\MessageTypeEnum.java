package net.lab1024.sa.admin.module.business.nfc.ws.domain;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * WebSocket Message Type Enum
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum MessageTypeEnum implements BaseEnum {

    /**
     * 心跳消息以保持连接活动状态
     */
    HEARTBEAT(0, "HEARTBEAT"),

   /**
     * 来自客户端的初始消息，用于声明其角色（TRANSMITTER 或 RECEIVER）
     */
    REGISTER_ROLE(1, "REGISTER_ROLE"),

    /**
     * 从接收器 （HCE） 到发射器（读卡器）的 APDU 命令
     */
    APDU_COMMAND(2, "APDU_COMMAND"),

    /**
     * 从发射器到接收器的 APDU 响应
     */
    APDU_RESPONSE(3, "APDU_RESPONSE"),

    /**
     * 通知双方已连接且会话已配对
     */
    PAIRED(10, "PAIRED"),

    /**
     * 来自服务器的一般状态更新
     */
    STATUS_UPDATE(11, "STATUS_UPDATE"),

    /**
     * 来自客户的会话即将结束的通知
     */
    SESSION_END(12, "SESSION_END"),

    /**
     * 来自客户端的状态更新通知（如：卡片已连接/已断开）
     */
    CLIENT_STATE_UPDATE(13, "CLIENT_STATE_UPDATE"),

    /**
     * 卡片信息消息
     */
    CARD_INFO(14, "CARD_INFO"),

    /**
     * 卡片移除消息
     */
    CARD_REMOVED(15, "CARD_REMOVED"),

   /**
     * 来自服务器的错误消息
     */
    ERROR(99, "ERROR");

    private final Integer value;
    private final String desc;
} 