package net.lab1024.sa.admin.module.business.nfc.ws;

import com.fasterxml.jackson.databind.ObjectMapper;
import net.lab1024.sa.admin.module.business.nfc.ws.domain.ApduCommandPayload;
import net.lab1024.sa.admin.module.business.nfc.ws.domain.ApduPayload;
import net.lab1024.sa.admin.module.business.nfc.ws.domain.ApduResponsePayload;
import net.lab1024.sa.admin.module.business.nfc.ws.domain.MessageTypeEnum;
import net.lab1024.sa.admin.module.business.nfc.ws.domain.WebSocketMessage;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试APDU Payload兼容性处理
 *
 * <AUTHOR>
 */
public class ApduPayloadCompatibilityTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    @DisplayName("测试ApduCommandPayload序列化和反序列化")
    void testApduCommandPayloadSerialization() throws Exception {
        // 创建ApduCommandPayload
        ApduCommandPayload commandPayload = new ApduCommandPayload(
                "test-request-id",
                "test-session-id", 
                "00A4040007A000000003101000",
                System.currentTimeMillis()
        );

        // 序列化
        String json = objectMapper.writeValueAsString(commandPayload);
        assertNotNull(json);
        assertTrue(json.contains("test-request-id"));
        assertTrue(json.contains("00A4040007A000000003101000"));

        // 反序列化
        ApduCommandPayload deserialized = objectMapper.readValue(json, ApduCommandPayload.class);
        assertEquals("test-request-id", deserialized.getRequestId());
        assertEquals("test-session-id", deserialized.getSessionId());
        assertEquals("00A4040007A000000003101000", deserialized.getApdu());
    }

    @Test
    @DisplayName("测试ApduCommandPayload转换为ApduPayload")
    void testApduCommandPayloadConversion() {
        ApduCommandPayload commandPayload = new ApduCommandPayload(
                "test-request-id",
                "test-session-id",
                "00A4040007A000000003101000", 
                System.currentTimeMillis()
        );

        ApduPayload simplePayload = commandPayload.toApduPayload();
        assertNotNull(simplePayload);
        assertEquals("00A4040007A000000003101000", simplePayload.getApdu());
    }

    @Test
    @DisplayName("测试WebSocketMessage与ApduCommandPayload的兼容性")
    void testWebSocketMessageWithApduCommandPayload() throws Exception {
        // 模拟Android客户端发送的消息
        String androidMessage = """
                {
                    "type": "APDU_COMMAND",
                    "payload": {
                        "@class": "net.lab1024.sa.admin.module.business.nfc.ws.domain.ApduCommandPayload",
                        "requestId": "50648637-248c-4da6-955e-929e7e8245e1",
                        "sessionId": "e80c2815-e9bf-4901-bfbb-2497180ccf2c",
                        "apdu": "00A4040007A000000003101000",
                        "timestamp": 1754158049987
                    }
                }
                """;

        // 测试反序列化
        WebSocketMessage<?> message = objectMapper.readValue(androidMessage, WebSocketMessage.class);
        assertNotNull(message);
        assertEquals(MessageTypeEnum.APDU_COMMAND, message.getType());
        assertNotNull(message.getPayload());

        // 测试payload转换
        ApduCommandPayload commandPayload = objectMapper.convertValue(message.getPayload(), ApduCommandPayload.class);
        assertNotNull(commandPayload);
        assertEquals("50648637-248c-4da6-955e-929e7e8245e1", commandPayload.getRequestId());
        assertEquals("00A4040007A000000003101000", commandPayload.getApdu());
    }

    @Test
    @DisplayName("测试ApduResponsePayload功能")
    void testApduResponsePayload() throws Exception {
        ApduResponsePayload responsePayload = new ApduResponsePayload(
                "test-request-id",
                "test-session-id",
                "9000",
                System.currentTimeMillis()
        );

        // 测试序列化
        String json = objectMapper.writeValueAsString(responsePayload);
        assertNotNull(json);

        // 测试转换
        ApduPayload simplePayload = responsePayload.toApduPayload();
        assertEquals("9000", simplePayload.getApdu());
    }

    @Test
    @DisplayName("测试向后兼容性 - 简单ApduPayload仍然工作")
    void testBackwardCompatibility() throws Exception {
        // 创建简单的ApduPayload消息
        ApduPayload simplePayload = new ApduPayload("00A4040007A000000003101000");
        WebSocketMessage<ApduPayload> message = new WebSocketMessage<>(MessageTypeEnum.APDU_COMMAND, simplePayload);

        // 序列化
        String json = objectMapper.writeValueAsString(message);
        assertNotNull(json);

        // 反序列化
        WebSocketMessage<?> deserializedMessage = objectMapper.readValue(json, WebSocketMessage.class);
        assertNotNull(deserializedMessage);
        assertEquals(MessageTypeEnum.APDU_COMMAND, deserializedMessage.getType());

        // 验证可以转换为ApduPayload
        ApduPayload deserializedPayload = objectMapper.convertValue(deserializedMessage.getPayload(), ApduPayload.class);
        assertEquals("00A4040007A000000003101000", deserializedPayload.getApdu());
    }
}
