#!/bin/bash

# WebSocket日志分析脚本
# 用于分析增强后的WebSocket日志

LOG_FILE=${1:-"logs/websocket.log"}

if [ ! -f "$LOG_FILE" ]; then
    echo "日志文件不存在: $LOG_FILE"
    echo "用法: $0 [日志文件路径]"
    exit 1
fi

echo "=== WebSocket日志分析报告 ==="
echo "日志文件: $LOG_FILE"
echo "分析时间: $(date)"
echo

# 1. 连接统计
echo "📊 连接统计:"
echo "  总连接数: $(grep -c "WebSocket连接成功" "$LOG_FILE")"
echo "  总断开数: $(grep -c "WebSocket断开" "$LOG_FILE")"
echo "  配对成功数: $(grep -c "配对成功" "$LOG_FILE")"
echo

# 2. APDU消息统计
echo "🔄 APDU消息统计:"
echo "  APDU指令数: $(grep -c "APDU指令" "$LOG_FILE")"
echo "  APDU响应数: $(grep -c "APDU响应" "$LOG_FILE")"
echo "  APDU转发成功数: $(grep -c "APDU转发成功" "$LOG_FILE")"
echo "  APDU转发失败数: $(grep -c "APDU转发失败" "$LOG_FILE")"
echo

# 3. 卡片信息统计
echo "💳 卡片信息统计:"
echo "  卡片信息接收数: $(grep -c "卡片信息接收" "$LOG_FILE")"
echo "  卡片信息转发成功数: $(grep -c "卡片信息转发成功" "$LOG_FILE")"
echo "  卡片信息转发失败数: $(grep -c "卡片信息转发失败" "$LOG_FILE")"
echo "  卡片移除通知数: $(grep -c "卡片移除通知成功" "$LOG_FILE")"
echo

# 4. 错误统计
echo "❌ 错误统计:"
echo "  总错误数: $(grep -c "❌" "$LOG_FILE")"
echo "  连接错误: $(grep -c "WebSocket错误" "$LOG_FILE")"
echo "  注册拒绝: $(grep -c "注册拒绝" "$LOG_FILE")"
echo "  无效角色: $(grep -c "无效角色" "$LOG_FILE")"
echo

# 5. 最近的APDU交互
echo "🔄 最近10条APDU交互:"
grep "APDU" "$LOG_FILE" | tail -10
echo

# 6. 最近的错误
echo "❌ 最近10条错误:"
grep "❌" "$LOG_FILE" | tail -10
echo

# 7. 活跃用户统计
echo "👥 活跃用户统计 (最近100条连接):"
grep "WebSocket连接成功" "$LOG_FILE" | tail -100 | \
    grep -o "employeeId=[0-9]*" | sort | uniq -c | sort -nr | head -10
echo

# 8. APDU数据长度分析
echo "📏 APDU数据长度分析:"
echo "  最常见的APDU长度:"
grep "APDU数据详情.*长度=" "$LOG_FILE" | \
    grep -o "长度=[0-9]* bytes" | sort | uniq -c | sort -nr | head -5
echo

echo "=== 分析完成 ==="
