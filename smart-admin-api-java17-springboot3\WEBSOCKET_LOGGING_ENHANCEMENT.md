# WebSocket 消息日志增强说明

## 概述

为了更好地监控和调试NFC WebSocket中继系统，我们对日志记录进行了全面增强，特别是对APDU消息的实时展示。

## 增强的日志功能

### 1. 📨 消息接收日志

每当收到WebSocket消息时，系统会记录：
```
📨 [WebSocket收到消息] employeeId=123, session=abc-def-123, messageType=APDU_COMMAND
📨 [消息内容] {"type":"APDU_COMMAND","payload":{"@class":"...","apdu":"00A4040007A0000002471001"}}
```

### 2. 🔄 APDU消息详细日志

#### APDU指令日志
```
📤 [APDU指令] employeeId=123, session=abc-def-123, APDU数据=00A4040007A0000002471001
🔄 [APDU数据详情] 长度=14 bytes, 内容=00A4040007A0000002471001
✅ [APDU转发成功] 从 employeeId=123 (session=abc-def-123) 转发到 employeeId=456 (session=def-ghi-456)
🔄 [APDU转发] 123 -> 456, 数据=00A4040007A0000002471001
```

#### APDU响应日志
```
📥 [APDU响应] employeeId=456, session=def-ghi-456, APDU数据=9000
🔄 [APDU数据详情] 长度=2 bytes, 内容=9000
✅ [APDU转发成功] 从 employeeId=456 (session=def-ghi-456) 转发到 employeeId=123 (session=abc-def-123)
🔄 [APDU转发] 456 -> 123, 数据=9000
```

### 3. 📤 消息发送日志

每当发送WebSocket消息时，系统会记录：
```
📤 [WebSocket发送消息] employeeId=123, session=abc-def-123, messageType=APDU_COMMAND
📤 [发送APDU指令] employeeId=123, APDU数据=00A4040007A0000002471001
🔄 [APDU发送详情] 长度=14 bytes, 内容=00A4040007A0000002471001
📤 [消息内容] {"type":"APDU_COMMAND","payload":{"apdu":"00A4040007A0000002471001"}}
✅ [消息发送成功] employeeId=123, messageType=APDU_COMMAND
```

### 4. 🔐 角色注册和配对日志

```
🔐 [角色注册] employeeId=123, session=abc-def-123, role=TRANSMITTER
🤝 [配对成功] employeeId=123 与 employeeId=456 配对成功
🤝 [配对详情] session1=abc-def-123, session2=def-ghi-456
💾 [交易创建] employeeId=123, transactionId=789
```

### 5. 💳 卡片管理日志

#### 卡片信息
```
💳 [处理卡片信息] employeeId=123, session=abc-def-123
💳 [卡片信息接收] employeeId=123, cardId=869FD29B, cardType=Mifare Classic, technology=android.nfc.tech.IsoDep, size=1024, manufacturer=NXP
✅ [卡片信息转发成功] 从 employeeId=123 转发到 employeeId=456, cardId=869FD29B
```

#### 卡片移除
```
🚫 [处理卡片移除] employeeId=123, session=abc-def-123
🚫 [卡片移除详情] employeeId=123, cardId=869FD29B, reason=USER_REMOVED, timestamp=1691234567890
✅ [卡片移除通知成功] 从 employeeId=123 通知到 employeeId=456
```

### 6. 🔗 连接管理日志

#### 连接建立
```
✅ [WebSocket连接成功] employeeId=123, employeeName='张三', session=abc-def-123
🔗 [连接详情] session=abc-def-123, token前缀=eyJhbGciO..., loginDevice=ANDROID
```

#### 连接断开
```
🔌 [WebSocket断开] employeeId=123, session=abc-def-123
📢 [通知伙伴] employeeId=123 断开连接, 通知伙伴 employeeId=456, partnerSession=def-ghi-456
🧹 [清理会话] employeeId=123, session=abc-def-123
💾 [交易中断] employeeId=123, transactionId=789, 原因: 连接意外断开
```

### 7. ❌ 错误处理日志

```
❌ [注册拒绝] employeeId=123, 原因: 会话已存在, error=Session already active for user 123
❌ [APDU转发失败] 伙伴设备离线. employeeId=123, APDU数据=00A4040007A0000002471001
❌ [WebSocket错误] employeeId=123, session=abc-def-123, error=Connection reset
❌ [错误详情] 错误类型=IOException, 错误消息=Connection reset
```

## 日志级别说明

- **INFO级别**: 所有业务流程日志，包括消息收发、APDU详情、连接管理等
- **WARN级别**: 警告信息，如伙伴设备离线、无效参数等
- **ERROR级别**: 错误信息，如连接失败、消息发送失败等
- **DEBUG级别**: 已移除，所有重要信息都提升到INFO级别

## APDU消息监控特性

### 实时APDU数据展示
- **完整APDU内容**: 显示完整的十六进制APDU数据
- **数据长度**: 自动计算并显示APDU数据的字节长度
- **方向标识**: 清楚标识是指令(📤)还是响应(📥)
- **转发跟踪**: 完整跟踪APDU在设备间的转发过程

### 消息流跟踪
每个APDU消息都会记录：
1. 接收方的详细信息
2. APDU数据的完整内容和长度
3. 转发目标的详细信息
4. 转发成功/失败状态

## 使用建议

### 生产环境
- 建议将日志级别设置为INFO，以获取完整的业务流程信息
- 可以通过grep等工具过滤特定类型的日志：
  ```bash
  # 只查看APDU相关日志
  grep "APDU" application.log
  
  # 查看特定用户的日志
  grep "employeeId=123" application.log
  
  # 查看错误日志
  grep "❌" application.log
  ```

### 开发调试
- 所有消息内容都会完整记录，便于调试
- 可以通过session ID跟踪特定连接的完整生命周期
- APDU数据的十六进制展示便于分析NFC通信协议

## 性能考虑

- 日志记录对性能影响很小
- 如果需要减少日志量，可以调整特定logger的级别
- 建议在生产环境中定期轮转日志文件

这些增强的日志功能将大大提高系统的可观测性和调试效率，特别是对于APDU消息的实时监控。

## 配置文件

### 1. 日志配置示例
项目中包含了 `logback-websocket-enhanced.xml` 配置文件，提供了：
- WebSocket专用日志文件
- APDU专用日志文件（只记录包含APDU的日志）
- 日志轮转和大小控制

### 2. 日志分析脚本
提供了 `analyze-websocket-logs.sh` 脚本，可以快速分析日志文件：
```bash
# 分析默认日志文件
./analyze-websocket-logs.sh

# 分析指定日志文件
./analyze-websocket-logs.sh logs/websocket.log
```

脚本提供的分析功能：
- 连接统计（连接数、断开数、配对数）
- APDU消息统计（指令数、响应数、转发成功/失败数）
- 卡片信息统计
- 错误统计和分类
- 最近的APDU交互记录
- 活跃用户统计
- APDU数据长度分析

## 实施完成

✅ **已完成的增强功能：**
1. 消息接收日志增强（onMessage方法）
2. APDU消息详细日志（handleApduRelay方法）
3. 消息发送日志增强（sendMessage方法）
4. 角色注册和配对日志（handleRegisterRole方法）
5. 连接管理日志（onOpen, onClose方法）
6. 错误处理日志（onError方法）
7. 卡片信息处理日志（handleCardInfo方法）
8. 卡片移除处理日志（handleCardRemoved方法）
9. 会话关闭日志（closeSession方法）

所有日志都使用了emoji图标和中文描述，便于快速识别和理解。特别是APDU消息的日志，提供了完整的数据内容、长度信息和转发跟踪，满足了实时监控的需求。
