package net.lab1024.sa.admin.module.business.nfc.ws;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import net.lab1024.sa.admin.module.business.nfc.ws.domain.CardInfoPayload;
import net.lab1024.sa.admin.module.business.nfc.ws.domain.MessageTypeEnum;
import net.lab1024.sa.admin.module.business.nfc.ws.domain.WebSocketMessage;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试CardInfoPayload字段名兼容性处理
 *
 * <AUTHOR>
 */
public class CardInfoPayloadCompatibilityTest {

    private final ObjectMapper objectMapper = createConfiguredObjectMapper();

    /**
     * 创建配置好的ObjectMapper，模拟NfcRelayEndpoint中的配置
     */
    private ObjectMapper createConfiguredObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
        return mapper;
    }

    @Test
    @DisplayName("测试CardInfoPayload支持下划线字段名")
    void testCardInfoPayloadWithUnderscoreFields() throws Exception {
        // 模拟Android客户端发送的消息（使用下划线字段名）
        String androidMessage = """
                {
                    "type": "CARD_INFO",
                    "payload": {
                        "@class": "net.lab1024.sa.admin.module.business.nfc.ws.domain.CardInfoPayload",
                        "card_id": "04:12:34:56:78:90:AB",
                        "card_type": "MIFARE_CLASSIC",
                        "technology": "NfcA",
                        "size": "1K",
                        "manufacturer": "NXP"
                    }
                }
                """;

        // 测试反序列化
        WebSocketMessage<?> message = objectMapper.readValue(androidMessage, WebSocketMessage.class);
        assertNotNull(message);
        assertEquals(MessageTypeEnum.CARD_INFO, message.getType());
        assertNotNull(message.getPayload());

        // 测试payload转换
        CardInfoPayload cardInfoPayload = objectMapper.convertValue(message.getPayload(), CardInfoPayload.class);
        assertNotNull(cardInfoPayload);
        assertEquals("04:12:34:56:78:90:AB", cardInfoPayload.getCardId());
        assertEquals("MIFARE_CLASSIC", cardInfoPayload.getCardType());
        assertEquals("NfcA", cardInfoPayload.getTechnology());
        assertEquals("1K", cardInfoPayload.getSize());
        assertEquals("NXP", cardInfoPayload.getManufacturer());
    }

    @Test
    @DisplayName("测试CardInfoPayload支持驼峰字段名（向后兼容）")
    void testCardInfoPayloadWithCamelCaseFields() throws Exception {
        // 模拟使用驼峰字段名的消息
        String camelCaseMessage = """
                {
                    "type": "CARD_INFO",
                    "payload": {
                        "@class": "net.lab1024.sa.admin.module.business.nfc.ws.domain.CardInfoPayload",
                        "cardId": "04:12:34:56:78:90:AB",
                        "cardType": "MIFARE_CLASSIC",
                        "technology": "NfcA",
                        "size": "1K",
                        "manufacturer": "NXP"
                    }
                }
                """;

        // 测试反序列化
        WebSocketMessage<?> message = objectMapper.readValue(camelCaseMessage, WebSocketMessage.class);
        assertNotNull(message);
        assertEquals(MessageTypeEnum.CARD_INFO, message.getType());

        // 测试payload转换
        CardInfoPayload cardInfoPayload = objectMapper.convertValue(message.getPayload(), CardInfoPayload.class);
        assertNotNull(cardInfoPayload);
        assertEquals("04:12:34:56:78:90:AB", cardInfoPayload.getCardId());
        assertEquals("MIFARE_CLASSIC", cardInfoPayload.getCardType());
    }

    @Test
    @DisplayName("测试CardInfoPayload序列化输出使用下划线格式")
    void testCardInfoPayloadSerialization() throws Exception {
        // 创建CardInfoPayload对象
        CardInfoPayload cardInfo = new CardInfoPayload(
                "04:12:34:56:78:90:AB",
                "MIFARE_CLASSIC",
                "NfcA"
        );
        cardInfo.setSize("1K");
        cardInfo.setManufacturer("NXP");

        WebSocketMessage<CardInfoPayload> message = new WebSocketMessage<>(MessageTypeEnum.CARD_INFO, cardInfo);

        // 序列化
        String json = objectMapper.writeValueAsString(message);
        assertNotNull(json);

        // 验证输出使用下划线格式
        assertTrue(json.contains("card_id"));
        assertTrue(json.contains("card_type"));
        assertTrue(json.contains("04:12:34:56:78:90:AB"));
        assertTrue(json.contains("MIFARE_CLASSIC"));
    }

    @Test
    @DisplayName("测试忽略未知字段不会导致反序列化失败")
    void testIgnoreUnknownFields() throws Exception {
        // 包含未知字段的消息
        String messageWithUnknownFields = """
                {
                    "type": "CARD_INFO",
                    "payload": {
                        "@class": "net.lab1024.sa.admin.module.business.nfc.ws.domain.CardInfoPayload",
                        "card_id": "04:12:34:56:78:90:AB",
                        "card_type": "MIFARE_CLASSIC",
                        "unknown_field": "should_be_ignored",
                        "another_unknown": 12345
                    }
                }
                """;

        // 应该能够成功反序列化，忽略未知字段
        assertDoesNotThrow(() -> {
            WebSocketMessage<?> message = objectMapper.readValue(messageWithUnknownFields, WebSocketMessage.class);
            CardInfoPayload cardInfo = objectMapper.convertValue(message.getPayload(), CardInfoPayload.class);
            assertEquals("04:12:34:56:78:90:AB", cardInfo.getCardId());
            assertEquals("MIFARE_CLASSIC", cardInfo.getCardType());
        });
    }

    @Test
    @DisplayName("测试混合字段名格式")
    void testMixedFieldNameFormats() throws Exception {
        // 混合使用驼峰和下划线字段名
        String mixedMessage = """
                {
                    "type": "CARD_INFO",
                    "payload": {
                        "@class": "net.lab1024.sa.admin.module.business.nfc.ws.domain.CardInfoPayload",
                        "card_id": "04:12:34:56:78:90:AB",
                        "cardType": "MIFARE_CLASSIC",
                        "technology": "NfcA"
                    }
                }
                """;

        // 应该能够正确处理混合格式
        WebSocketMessage<?> message = objectMapper.readValue(mixedMessage, WebSocketMessage.class);
        CardInfoPayload cardInfo = objectMapper.convertValue(message.getPayload(), CardInfoPayload.class);
        
        assertEquals("04:12:34:56:78:90:AB", cardInfo.getCardId());
        assertEquals("MIFARE_CLASSIC", cardInfo.getCardType());
        assertEquals("NfcA", cardInfo.getTechnology());
    }
}
