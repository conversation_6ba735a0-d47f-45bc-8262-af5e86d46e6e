# 🔧 WebSocket字段名兼容性修复

## 问题描述

在NFC WebSocket通信中，Android客户端和服务端存在字段命名格式不一致的问题：

### 原始错误
```
Unrecognized field "card_id" (class net.lab1024.sa.admin.module.business.nfc.ws.domain.CardInfoPayload), not marked as ignorable
```

### 根本原因
- **Android客户端**: 使用下划线命名格式 (`card_id`, `card_type`)
- **服务端**: 使用驼峰命名格式 (`cardId`, `cardType`)
- **Jackson反序列化**: 严格匹配字段名，不匹配时抛出异常

## 解决方案

### 1. 双重兼容性策略

#### A. CardInfoPayload类级别修复
```java
@JsonProperty("cardId")
@JsonAlias({"card_id"})
private String cardId;

@JsonProperty("cardType") 
@JsonAlias({"card_type"})
private String cardType;
```

#### B. 全局ObjectMapper配置
```java
private static ObjectMapper createConfiguredObjectMapper() {
    ObjectMapper mapper = new ObjectMapper();
    // 忽略未知字段，避免因为字段名不匹配导致的反序列化失败
    mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    // 设置属性命名策略为snake_case，用于序列化输出
    mapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
    return mapper;
}
```

### 2. 统一ObjectMapper使用

将所有 `new ObjectMapper()` 替换为统一的静态实例：
- ✅ `onMessage()` - 消息反序列化
- ✅ `handleRegisterRole()` - 角色注册
- ✅ `handleApduRelay()` - APDU转发
- ✅ `handleClientStateUpdate()` - 状态更新
- ✅ `handleSessionEnd()` - 会话结束
- ✅ `handleCardInfo()` - 卡片信息
- ✅ `handleCardRemoved()` - 卡片移除
- ✅ `sendMessage()` - 消息发送

## 兼容性矩阵

| 输入格式 | 字段名 | 处理结果 | 说明 |
|---------|--------|----------|------|
| Android客户端 | `card_id` | ✅ 成功 | 通过@JsonAlias支持 |
| 测试用例 | `cardId` | ✅ 成功 | 通过@JsonProperty支持 |
| 混合格式 | `card_id` + `cardType` | ✅ 成功 | 两种注解同时生效 |
| 未知字段 | `unknown_field` | ✅ 忽略 | FAIL_ON_UNKNOWN_PROPERTIES=false |

## 输出格式

所有序列化输出统一使用下划线格式：
```json
{
  "type": "CARD_INFO",
  "payload": {
    "card_id": "04:12:34:56:78:90:AB",
    "card_type": "MIFARE_CLASSIC",
    "technology": "NfcA"
  }
}
```

## 修复验证

### 单元测试
- ✅ `CardInfoPayloadCompatibilityTest` - 字段名兼容性测试
- ✅ `ApduPayloadCompatibilityTest` - APDU载体兼容性测试

### 集成测试
1. **Android客户端消息** - 使用下划线字段名
2. **服务端处理** - 正确解析并转发
3. **输出格式** - 统一使用下划线格式

## 受益的消息类型

| 消息类型 | Payload类 | 受影响字段 | 修复状态 |
|---------|-----------|------------|----------|
| `CARD_INFO` | `CardInfoPayload` | `cardId`, `cardType` | ✅ 已修复 |
| `APDU_COMMAND` | `ApduCommandPayload` | `requestId`, `sessionId` | ✅ 已支持 |
| `APDU_RESPONSE` | `ApduResponsePayload` | `requestId`, `sessionId` | ✅ 已支持 |
| `CLIENT_STATE_UPDATE` | `ClientStateUpdatePayload` | 无驼峰字段 | ✅ 无需修复 |
| `SESSION_END` | `SessionEndPayload` | 无驼峰字段 | ✅ 无需修复 |

## 最佳实践

### 1. 新增Payload类规范
```java
@JsonProperty("fieldName")
@JsonAlias({"field_name"})
private String fieldName;
```

### 2. 测试覆盖
- 下划线格式输入测试
- 驼峰格式输入测试  
- 混合格式输入测试
- 未知字段忽略测试

### 3. 文档更新
- API文档说明支持两种字段名格式
- 客户端开发指南推荐使用下划线格式

## 预期效果

修复后，以下场景都能正常工作：

1. **Android客户端发送** `card_id` → ✅ 服务端正确解析
2. **测试用例使用** `cardId` → ✅ 向后兼容
3. **服务端输出** → 统一使用 `card_id` 格式
4. **POS机交易** → 不再因字段名不匹配而中断

这个修复确保了NFC WebSocket通信的稳定性和兼容性，解决了POS机处理中断的问题。
