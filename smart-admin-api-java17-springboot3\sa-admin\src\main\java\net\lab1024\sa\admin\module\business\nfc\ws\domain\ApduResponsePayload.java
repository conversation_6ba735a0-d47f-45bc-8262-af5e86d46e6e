package net.lab1024.sa.admin.module.business.nfc.ws.domain;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

/**
 * APDU响应消息的扩展载体，包含请求追踪信息。
 * 用于Android客户端发送的APDU响应，包含额外的元数据字段。
 *
 * <AUTHOR>
 */
@Getter
public class ApduResponsePayload {

    /**
     * 请求唯一标识符，用于追踪和调试
     */
    private final String requestId;

    /**
     * 会话标识符，用于关联同一会话的多个请求
     */
    private final String sessionId;

    /**
     * APDU响应数据（十六进制字符串）
     */
    private final String apdu;

    /**
     * 时间戳，记录响应发送时间
     */
    private final Long timestamp;

    @JsonCreator
    public ApduResponsePayload(
            @JsonProperty("requestId") String requestId,
            @JsonProperty("sessionId") String sessionId,
            @JsonProperty("apdu") String apdu,
            @JsonProperty("timestamp") Long timestamp) {
        this.requestId = requestId;
        this.sessionId = sessionId;
        this.apdu = apdu;
        this.timestamp = timestamp;
    }

    /**
     * 转换为简单的ApduPayload格式，用于向后兼容
     *
     * @return ApduPayload对象
     */
    public ApduPayload toApduPayload() {
        return new ApduPayload(this.apdu);
    }
}
